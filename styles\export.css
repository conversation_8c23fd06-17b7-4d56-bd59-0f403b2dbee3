/**
 * @file 导出模式样式文件
 * @description 导出模式专用样式，解决样式冲突，确保导出一致性
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 导出模式基础设置 - 解决通用样式冲突 */
/* 导出模式通用样式 - 避免影响图片元素 */
.export-mode * {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    -webkit-hyphens: auto !important;
    hyphens: auto !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    /* 防止文字重叠的关键设置 */
    box-sizing: border-box !important;
    position: relative !important;
}

/* 导出模式下图片元素例外 - 避免通用样式影响图片显示 */
.export-mode img {
    position: static !important; /* 重置图片定位，避免通用样式干扰 */
}

/* 导出模式下图片容器保持正确定位 */
.export-mode .document-header-image-container,
.export-mode .document-header {
    position: relative !important;
}

/* 导出模式下页脚保持绝对定位 - 修复页脚定位问题 */
.export-mode .company-footer-image-container,
.export-mode .unified-document-footer {
    position: fixed !important; /* 改为fixed定位，相对于视口 */
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: var(--footer-height) !important;
    z-index: var(--z-index-footer) !important;
    width: 100% !important;
    background-color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 5px !important;
    box-sizing: border-box !important;
    margin: 0 !important;
}

/* 导出模式下确保receipt-container不影响页脚定位 */
.export-mode .receipt-container {
    position: static !important; /* 移除相对定位，避免影响页脚定位上下文 */
}

/* 导出模式下印章容器保持绝对定位 */
.export-mode .company-stamp {
    position: absolute !important;
}
/* #endregion */

/* #region 导出模式图片样式 - 与预览模式完全一致 */
/* 页眉图片导出样式 - 确保与预览模式一致 */
.export-mode .document-header-image-container,
.export-mode .document-header {
    position: relative !important;
    width: 100% !important;
    height: var(--header-height) !important;
    margin-bottom: 15px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    background: white !important;
    box-sizing: border-box !important;
}

.export-mode .document-header-image-container img,
.export-mode .document-header img {
    height: 100% !important;
    width: 100% !important; /* 修改为100%以配合cover填充 */
    object-fit: cover !important; /* 关键：完全填满130px区域，保持宽高比 */
    object-position: center !important;
    margin: 0 !important;
    display: block !important;
    
    /* 强制防止拉伸变形 - 关键修复 */
    min-width: 0 !important;
    min-height: 0 !important;
    flex-shrink: 0 !important;
    
    /* 高质量图片渲染 */
    image-rendering: high-quality !important;
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    
    /* 防止图片模糊和压缩 */
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    
    /* 硬件加速 */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    
    /* 字体平滑 */
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* 页脚图片导出样式 - 完全填满110px区域 */
.export-mode .unified-document-footer img,
.export-mode .company-footer-image-container img {
    height: 100% !important;
    width: 100% !important; /* 修改为100%以配合cover填充 */
    object-fit: cover !important; /* 关键：完全填满110px区域，保持宽高比 */
    object-position: center !important;
    margin: 0 !important;
    display: block !important;
    
    /* 防拉伸变形属性 */
    min-width: 0 !important;
    min-height: 0 !important;
    flex-shrink: 0 !important;
    
    /* 高质量渲染 */
    image-rendering: high-quality !important;
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    
    /* 清理样式 */
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* 印章图片导出样式 - 确保完整显示不被裁切，最高层级，完全透明背景 */
.export-mode .company-stamp {
    position: absolute !important;
    bottom: var(--stamp-bottom-offset) !important;
    right: var(--stamp-right-offset) !important;
    z-index: var(--z-index-stamp) !important; /* 使用最高层级300 */
    width: 96px !important;
    height: 96px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: visible !important; /* 关键：允许内容可见，防止裁切 */
    box-sizing: border-box !important;
    /* 强制透明背景，避免遮挡底层内容 */
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.export-mode .company-stamp img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
    background-color: transparent !important;
    box-shadow: none !important;
    opacity: 0.9 !important; /* 调整为0.9，符合300DPI质量标准要求 */

    /* 使用混合模式实现真正的透明叠加 */
    mix-blend-mode: multiply !important; /* 正片叠底模式，实现真实印章效果 */
    isolation: auto !important; /* 允许混合模式生效 */

    /* 防拉伸变形属性 */
    min-width: 0 !important;
    min-height: 0 !important;
    flex-shrink: 0 !important;

    /* 高质量渲染 */
    image-rendering: high-quality !important;
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
}
/* #endregion */

/* #region 导出模式内容样式 - 确保边距一致性 */
/* 导出模式下的表格样式 */
.export-mode .items-table {
    margin-left: var(--content-margin-left) !important;
    margin-right: var(--content-margin-right) !important;
    width: calc(100% - var(--content-margin-left) - var(--content-margin-right)) !important;
    border-collapse: collapse !important;
    margin-top: 10px !important;
    margin-bottom: 15px !important;
}

/* 导出模式下的内容区域边距 */
.export-mode .notes-section,
.export-mode .company-info,
.export-mode .customer-info,
.export-mode .payment-info,
.export-mode .document-title {
    padding-left: var(--content-margin-left) !important;
    padding-right: var(--content-margin-right) !important;
    box-sizing: border-box !important;
}

/* 特殊处理：备注区域需要额外边距 */
.export-mode .notes-section {
    margin-left: var(--content-margin-left) !important;
    margin-right: var(--content-margin-right) !important;
    width: calc(100% - var(--content-margin-left) - var(--content-margin-right)) !important;
}

/* 导出模式下的总金额容器 */
.export-mode .total-amount-container {
    background-color: #ffffff !important;
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    padding: 12px 18px !important;
    margin: 15px var(--content-margin-left) !important;
    border-radius: 6px !important;
    box-shadow: none !important;
    min-width: 200px !important;
    max-width: 100% !important;
    z-index: var(--z-index-total) !important;
    position: relative !important;
    text-align: center !important;
}

.export-mode .total-amount-container h3 {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: bold !important;
    line-height: 1.4 !important;
    color: var(--primary-color) !important;
    text-shadow: none !important;
    white-space: nowrap !important;
}
/* #endregion */

/* #region 导出模式文字排版优化 */
/* 导出模式下的表格文字优化 */
.export-mode table {
    table-layout: fixed !important;
    width: 100% !important;
    border-collapse: collapse !important;
    border-spacing: 0 !important;
}

/* 导出模式下的表格单元格优化 - 统一字体设置 */
.export-mode .items-table th,
.export-mode .items-table td {
    padding: 8px !important;
    border: 1px solid #333 !important;
    text-align: left !important;
    vertical-align: top !important;
    font-size: var(--base-font-size) !important;
    line-height: 1.4 !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

/* 导出模式下的数字列右对齐 */
.export-mode .items-table td:nth-child(2),
.export-mode .items-table td:nth-child(3),
.export-mode .items-table td:nth-child(4) {
    text-align: right !important;
}

/* 导出模式下的文本容器优化 */
.export-mode div,
.export-mode span,
.export-mode p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    -webkit-hyphens: auto !important;
    hyphens: auto !important;
    line-height: 1.4 !important;
    box-sizing: border-box !important;
}

/* 导出模式下的标题优化 */
.export-mode h1,
.export-mode h2,
.export-mode h3 {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    line-height: 1.3 !important;
    margin: 8px 0 !important;
    box-sizing: border-box !important;
}

.export-mode table td,
.export-mode table th {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    -webkit-hyphens: auto !important;
    hyphens: auto !important;
    white-space: normal !important;
    vertical-align: top !important;
    line-height: 1.4 !important;
    padding: 6px 8px !important;
    box-sizing: border-box !important;
}
/* #endregion */

/* #region 导出模式容器优化 */
/* 导出模式下的预览容器 - 简化版本（DOM结构重组方案不再需要复杂的CSS修复） */
.export-mode #document-preview {
    /* 基础重置，主要用于传统导出方案的兼容性 */
    --preview-scale-factor: 1 !important;
    transform: none !important;
    width: var(--a4-width-px) !important;
    height: var(--a4-height-px) !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    box-shadow: none !important;
    position: relative !important;
    overflow: visible !important;
}

/* 简化的媒体查询覆盖 - DOM结构重组方案主要依赖隔离DOM，减少CSS复杂性 */
@media (max-width: 1024px) {
    .export-mode #document-preview {
        --preview-scale-factor: 1 !important;
        transform: none !important;
    }
}

/* 保留关键的横屏修复，用于传统导出方案的兼容性 */
@media (orientation: landscape) and (max-width: 1024px) {
    .export-mode #document-preview {
        --preview-scale-factor: 1 !important;
        transform: none !important;
        width: var(--a4-width-px) !important;
        height: var(--a4-height-px) !important;
    }
}

/* 导出模式下的文档容器 - 修复高度限制 */
.export-mode #document-container {
    width: var(--a4-width-px) !important;
    height: auto !important; /* 改为auto，让内容决定高度 */
    min-height: var(--a4-height-px) !important;
    max-height: none !important; /* 移除最大高度限制 */
    background: white !important;
    margin: 0 !important;
    padding-top: 20px !important;
    padding-bottom: calc(var(--footer-height) + 15px) !important;
    padding-left: var(--content-margin-left) !important; /* 统一为30px */
    padding-right: var(--content-margin-right) !important; /* 统一为30px */
    display: flex !important;
    flex-direction: column !important;
    font-size: var(--base-font-size) !important;
    line-height: var(--line-height) !important;
    color: var(--text-color) !important;
    overflow: visible !important; /* 改为visible，确保页脚可见 */
    box-sizing: border-box !important;
    position: relative !important;
}


/* #endregion */

/* #region 导出模式隐藏元素 */
/* 导出时隐藏不需要的元素 */
.export-mode .image-placeholder,
.export-mode .header-placeholder,
.export-mode .footer-placeholder,
.export-mode .stamp-placeholder {
    display: none !important;
}

.export-mode .empty-preview-message {
    display: none !important;
}

.export-mode .preview-status-indicator {
    display: none !important;
}

.export-mode .preview-zoom-controls {
    display: none !important;
}

.export-mode #preview-container::before,
.export-mode #document-preview::after {
    display: none !important;
}
/* #endregion */

/* #region 导出模式调试辅助 */
/* 导出模式调试边框 - 开发时使用 */
.export-mode.debug-borders * {
    outline: 1px solid rgba(255, 0, 0, 0.3) !important;
}

.export-mode.debug-borders img {
    outline: 2px solid rgba(0, 255, 0, 0.5) !important;
}

.export-mode.debug-borders .company-stamp {
    outline: 2px solid rgba(0, 0, 255, 0.5) !important;
}

/* 导出模式性能优化 */
.export-mode * {
    /* 禁用动画和过渡，提升导出性能 */
    animation: none !important;
    transition: none !important;
}
/* #endregion */
